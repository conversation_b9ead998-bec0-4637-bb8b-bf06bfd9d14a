import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { OffreService, Offre } from '../../../services/offre.service';
import { PostulationService } from '../../../services/postulation.service';
import { AuthService } from '../../../services/auth.service';
import { PostulationRequest } from '../../../models/postulation.model';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import Swal from 'sweetalert2';
import { FormBuilder, FormGroup } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { AlertsService } from '../../../services/alerts.service';
import { PostulationDialogComponent } from '../../../components/postulation-dialog/postulation-dialog.component';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  offres: Offre[] = [];
  filteredOffres: Offre[] = [];
  searchText: string = '';
  isProfileMenuOpen = false;
  currentUserId: number | null = null;
  currentUser: any;
  isLoading = false;
  isFiltering = false;
  filterForm: FormGroup;
  mesPostulations: any[] = [];
  postulationForm: FormGroup;

  constructor(
    private titleService: Title,
    private router: Router,
    private offreService: OffreService,
    private postulationService: PostulationService,
    private authService: AuthService,
    private alertsService: AlertsService,
    private dialog: MatDialog,
    private fb: FormBuilder
  ) {
    this.currentUserId = this.authService.getCurrentUserId();
    this.filterForm = this.fb.group({
      search: [''],
      domaine: [''],
      ville: [''],
      typeContrat: ['']
    });
    this.postulationForm = this.fb.group({
      cv: ['']
    });
  }

  ngOnInit() {
    this.titleService.setTitle('Tableau de bord candidat');
    this.loadCurrentUser();
    this.loadOffres();
    this.loadMesPostulations();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadCurrentUser() {
    // Données statiques d'utilisateur pour l'affichage
    this.currentUser = {
      id: 1,
      nom: 'Benali',
      prenom: 'Ahmed',
      email: '<EMAIL>',
      telephone: '+212 6 12 34 56 78',
      ville: 'Rabat'
    };
    this.currentUserId = 1;
  }

  private loadOffres() {
    this.isLoading = true;

    // Simulation d'un délai de chargement pour l'effet visuel
    setTimeout(() => {
      // Données statiques d'offres d'emploi
      this.offres = [
        {
          id: 1,
          titre: 'Développeur Full Stack Angular/Node.js',
          description: 'Nous recherchons un développeur Full Stack expérimenté pour rejoindre notre équipe dynamique. Vous travaillerez sur des projets innovants utilisant Angular, Node.js et les dernières technologies web. Responsabilités : développement d\'applications web, maintenance du code, collaboration avec l\'équipe UX/UI.',
          domaine: 'Informatique',
          ville: 'Rabat',
          typeContrat: 'CDI',
          salaire: '45000',
          heuresParSemaine: 40,
          dateLimite: new Date('2024-12-31'),
          competences: ['Angular', 'Node.js', 'TypeScript', 'MongoDB', 'Express.js'],
          recruteurId: 1,
          expanded: false
        },
        {
          id: 2,
          titre: 'Designer UX/UI Senior',
          description: 'Rejoignez notre équipe créative en tant que Designer UX/UI Senior. Vous concevrez des interfaces utilisateur intuitives et esthétiques pour nos applications web et mobiles. Vous travaillerez en étroite collaboration avec les développeurs et les chefs de produit.',
          domaine: 'Design',
          ville: 'Casablanca',
          typeContrat: 'CDI',
          salaire: '38000',
          heuresParSemaine: 40,
          dateLimite: new Date('2024-11-30'),
          competences: ['Figma', 'Adobe XD', 'Prototyping', 'User Research', 'Sketch'],
          recruteurId: 2,
          expanded: false
        },
        {
          id: 3,
          titre: 'Chef de Projet Digital',
          description: 'Nous cherchons un Chef de Projet Digital expérimenté pour piloter nos projets de transformation numérique. Vous coordonnerez les équipes techniques et métier, gérerez les budgets et assurerez le respect des délais.',
          domaine: 'Management',
          ville: 'Marrakech',
          typeContrat: 'CDI',
          salaire: '55000',
          heuresParSemaine: 40,
          dateLimite: new Date('2024-12-15'),
          competences: ['Gestion de projet', 'Agile', 'Scrum', 'Leadership', 'JIRA'],
          recruteurId: 3,
          expanded: false
        },
        {
          id: 4,
          titre: 'Développeur Mobile Flutter',
          description: 'Développeur mobile spécialisé en Flutter recherché pour créer des applications mobiles cross-platform innovantes et performantes. Vous développerez des applications pour iOS et Android en utilisant Flutter et Dart.',
          domaine: 'Informatique',
          ville: 'Fès',
          typeContrat: 'CDD',
          salaire: '35000',
          heuresParSemaine: 40,
          dateLimite: new Date('2024-11-20'),
          competences: ['Flutter', 'Dart', 'Firebase', 'API REST', 'Git'],
          recruteurId: 4,
          expanded: false
        },
        {
          id: 5,
          titre: 'Analyste Data / Data Scientist',
          description: 'Analyste Data pour extraire des insights précieux de nos données. Vous travaillerez avec des outils modernes d\'analyse et de visualisation pour aider à la prise de décision stratégique de l\'entreprise.',
          domaine: 'Data Science',
          ville: 'Tanger',
          typeContrat: 'Freelance',
          salaire: '40000',
          heuresParSemaine: 35,
          dateLimite: new Date('2024-12-10'),
          competences: ['Python', 'SQL', 'Power BI', 'Machine Learning', 'Pandas'],
          recruteurId: 5,
          expanded: false
        },
        {
          id: 6,
          titre: 'Ingénieur DevOps',
          description: 'Ingénieur DevOps pour automatiser nos processus de déploiement et maintenir notre infrastructure cloud. Vous travaillerez avec Docker, Kubernetes et les services AWS pour optimiser nos pipelines CI/CD.',
          domaine: 'Informatique',
          ville: 'Rabat',
          typeContrat: 'CDI',
          salaire: '50000',
          heuresParSemaine: 40,
          dateLimite: new Date('2024-12-20'),
          competences: ['Docker', 'Kubernetes', 'AWS', 'Jenkins', 'Terraform'],
          recruteurId: 6,
          expanded: false
        }
      ];

      this.filteredOffres = [...this.offres];
      this.isLoading = false;
    }, 1000); // Délai de 1 seconde pour simuler le chargement
  }

  private loadMesPostulations(): void {
    // Données statiques de postulations pour l'affichage
    this.mesPostulations = [
      {
        id: 1,
        offreId: 1,
        offreTitre: 'Développeur Full Stack Angular/Node.js',
        entreprise: 'TechCorp',
        statut: 'En attente',
        datePostulation: new Date('2024-11-01')
      },
      {
        id: 2,
        offreId: 3,
        offreTitre: 'Chef de Projet Digital',
        entreprise: 'DigitalSolutions',
        statut: 'Acceptée',
        datePostulation: new Date('2024-10-15')
      }
    ];
  }

  applyFilter() {
    if (!this.searchText) {
      this.filteredOffres = this.offres;
      return;
    }

    const searchLower = this.searchText.toLowerCase();
    this.filteredOffres = this.offres.filter(offre =>
      offre.titre.toLowerCase().includes(searchLower) ||
      offre.description.toLowerCase().includes(searchLower) ||
      offre.domaine.toLowerCase().includes(searchLower) ||
      offre.ville.toLowerCase().includes(searchLower) ||
      offre.typeContrat.toLowerCase().includes(searchLower) ||
      offre.competences.some((comp: string) => comp.toLowerCase().includes(searchLower))
    );
  }

  toggleProfileMenu() {
    this.isProfileMenuOpen = !this.isProfileMenuOpen;
  }

  async postuler(offre: Offre) {
    if (!this.currentUserId) {
      Swal.fire({
        icon: 'error',
        title: 'Erreur',
        text: 'Vous devez être connecté pour postuler.'
      });
      return;
    }

    if (this.postulationForm.valid) {
      const cvFile = this.postulationForm.get('cv')?.value;
      const { value: lettreMotivation } = await Swal.fire({
        title: 'Lettre de motivation',
        input: 'textarea',
        inputLabel: 'Veuillez rédiger votre lettre de motivation',
        inputPlaceholder: 'Écrivez votre lettre de motivation ici...',
        showCancelButton: true,
        inputValidator: (value) => {
          if (!value) {
            return 'La lettre de motivation est obligatoire';
          }
          return null;
        }
      });

      if (lettreMotivation) {
        const formData = new FormData();
        formData.append('offreId', offre.id.toString());
        formData.append('lettreMotivation', lettreMotivation);
        formData.append('cv', cvFile);

        this.postulationService.postuler({
          offreId: offre.id,
          lettreMotivation,
          cv: cvFile
        }).subscribe({
          next: (response) => {
            Swal.fire({
              icon: 'success',
              title: 'Candidature envoyée !',
              text: 'Votre candidature a été envoyée avec succès. Vous recevrez bientôt une réponse.',
              timer: 3000,
              showConfirmButton: false
            });
          },
          error: (error) => {
            console.error('Erreur lors de la postulation:', error);
            Swal.fire({
              icon: 'error',
              title: 'Erreur',
              text: 'Une erreur est survenue lors de l\'envoi de votre candidature. Veuillez réessayer.'
            });
          }
        });
      }
    }
  }

  sauvegarderOffre(offre: Offre) {
    // TODO: Implémenter la sauvegarde des offres
    Swal.fire({
      icon: 'info',
      title: 'Fonctionnalité à venir',
      text: 'La sauvegarde des offres sera bientôt disponible.'
    });
  }

  applyFilters(): void {
    this.isFiltering = true;
    const filter = this.filterForm.value;

    // Simulation d'un délai de filtrage
    setTimeout(() => {
      let filteredOffres = [...this.offres];

      if (filter.search) {
        const searchLower = filter.search.toLowerCase();
        filteredOffres = filteredOffres.filter(offre =>
          offre.titre.toLowerCase().includes(searchLower) ||
          offre.description.toLowerCase().includes(searchLower) ||
          offre.domaine.toLowerCase().includes(searchLower) ||
          offre.ville.toLowerCase().includes(searchLower)
        );
      }

      if (filter.domaine) {
        filteredOffres = filteredOffres.filter(offre =>
          offre.domaine.toLowerCase().includes(filter.domaine.toLowerCase())
        );
      }

      if (filter.ville) {
        filteredOffres = filteredOffres.filter(offre =>
          offre.ville.toLowerCase().includes(filter.ville.toLowerCase())
        );
      }

      if (filter.typeContrat) {
        filteredOffres = filteredOffres.filter(offre =>
          offre.typeContrat.toLowerCase().includes(filter.typeContrat.toLowerCase())
        );
      }

      this.filteredOffres = filteredOffres;
      this.isFiltering = false;
    }, 500); // Délai de 0.5 seconde pour simuler le filtrage
  }

  resetFilters(): void {
    this.filterForm.reset();
    this.filteredOffres = [...this.offres]; // Réinitialiser avec toutes les offres
  }

  postulerDialog(offre: Offre) {
    const dialogRef = this.dialog.open(PostulationDialogComponent, {
      width: '500px',
      data: { offreId: offre.id }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadMesPostulations();
      }
    });
  }

  deconnexion(): void {
    this.authService.logout();
  }

  logout(): void {
    this.authService.logout();
  }
}
